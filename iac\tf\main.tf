###############################
# Labels Module
###############################
module "labels" {
  # Update this source to use the Artifactory URL of a released version of the module: https://go.merck.com/iacreleases
  source      = "https://artifacts.merck.com/artifactory/generic-iac-shared-local/terraform/releases/iac-shared-tf-module-aws-labels-int_4.0.1.tgz"
  enabled     = true
  appname     = var.resource_vars.appname
  region      = var.resource_vars.region
  attributes  = var.resource_vars.attributes
  environment = var.resource_vars.environment
  label_order = var.resource_vars.label_order
}

locals {
  name                  = module.labels.id
  lambda_role_arn       = "arn:aws-cn:iam::************:role/deployment-role"
  eventbridge_role_name = "${local.name}-eventbridge-ecs-scheduler"

  # Define the ECS cluster ARN
  ecs_cluster_arn = "arn:aws-cn:ecs:cn-north-1:************:cluster/cgpt-dev-ecs"

  # Define the schedules for start and stop operations
  start_schedule = "cron(0 8 ? * MON-FRI *)"  # 8:00 AM Monday-Friday
  stop_schedule  = "cron(0 18 ? * MON-FRI *)" # 6:00 PM Monday-Friday

  # Define region and account number for static ARNs
  region     = "cn-north-1"
  account_no = "************"

  # IAM policies for Lambda to manage ECS services
  #  lambda_policy_arns = [
  #    "arn:aws-cn:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy",
  #    "arn:aws-cn:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole",
  #  ]

  # Lambda function source path
  lambda_source_path = "../../src/scheduler"
}

# We don't need a security group since we're not using VPC for this Lambda

# Create the Lambda and EventBridge
module "cron" {
  source            = "artifacts.merck.com/terraform-iac-shared__patterns/generic/aws"
  version           = "3.1.1"
  use_global_vpc_id = true
  global_vpc_id     = var.vpc_id

  lambda = {
    "${local.name}-ecs-scheduler" = {
      store_on_s3    = false
      create_package = true
      publish        = true
      handler        = "main.lambda_handler"
      runtime        = "python3.12"
      timeout        = "300"
      source_path    = local.lambda_source_path

      create_role = false
      lambda_role = local.lambda_role_arn

      # Environment variables
      environment_variables = {
        ECS_CLUSTER_ARN = local.ecs_cluster_arn
      }

      # Attach necessary policies
      #      attach_policies    = true
      #      number_of_policies = length(local.lambda_policy_arns)
      #      policies           = local.lambda_policy_arns

      # Additional policy statements for ECS service management
      #      attach_policy_statements = true
      #      policy_statements = {
      #        ecs_management = {
      #          effect = "Allow",
      #          actions = [
      #            "ecs:ListServices",
      #            "ecs:DescribeServices",
      #            "ecs:UpdateService"
      #          ],
      #          resources = ["*"]
      #        }
      #      }

      # Lambda trigger
      create_unqualified_alias_allowed_triggers = true
      create_current_version_allowed_triggers   = false
      allowed_triggers = {
        ExecutionFromEventsStart = {
          statement_id = "AllowExecutionFromEvents"
          action       = "lambda:InvokeFunction"
          service      = "events"
          # 使用静态构造的 ARN 而不是引用模块输出
          source_arn = "arn:aws-cn:events:${var.region}:${var.account_no}:rule/${local.name}-start-ecs-rule"
        }
        ExecutionFromEventsStop = {
          statement_id = "AllowExecutionFromEvents"
          action       = "lambda:InvokeFunction"
          service      = "events"
          # 使用静态构造的 ARN 而不是引用模块输出
          source_arn = "arn:aws-cn:events:${var.region}:${var.account_no}:rule/${local.name}-stop-ecs-rule"
        }
      }

      tags = var.tags
    }
  }

  eventbridge = {
    "${local.name}-ecs-scheduler" = {
      create_bus = false
      role_name  = local.eventbridge_role_name

      create_rules = true
      rules = {
        "${local.name}-start-ecs" = {
          description         = "Trigger Lambda to start ECS services"
          schedule_expression = local.start_schedule
        },
        "${local.name}-stop-ecs" = {
          description         = "Trigger Lambda to stop ECS services"
          schedule_expression = local.stop_schedule
        }
      }

      targets = {
        "${local.name}-start-ecs" = [
          {
            name = "${local.name}-start-ecs"
            # Use a static ARN format instead of referencing the module output
            arn = "arn:aws-cn:lambda:${local.region}:${local.account_no}:function:${local.name}-ecs-scheduler"
            input = jsonencode({
              action  = "start"
              cluster = local.ecs_cluster_arn
              # Optional: Specify specific services to start
              services = ["Vector_DB"]
              # Optional: Use patterns to match services (supports wildcards)
              service_patterns = ["Moyan_*"]
              # Optional: Set default desired count for all services
              desired_count = 2
              # Optional: Set specific desired counts for individual services
              service_desired_counts = {
                "Vector_DB"      = 1
                "Moyan_Frontend" = 3
              }
            })
          }
        ],
        "${local.name}-stop-ecs" = [
          {
            name = "${local.name}-stop-ecs"
            # Use a static ARN format instead of referencing the module output
            arn = "arn:aws-cn:lambda:${local.region}:${local.account_no}:function:${local.name}-ecs-scheduler"
            input = jsonencode({
              action  = "stop"
              cluster = local.ecs_cluster_arn
              # Optional: Specify specific services to stop
              services = ["Vector_DB"]
              # Optional: Use patterns to match services (supports wildcards)
              service_patterns = ["Moyan_*"]
            })
          }
        ]
      }
    }
  }

  default_tags = var.default_tags
}

