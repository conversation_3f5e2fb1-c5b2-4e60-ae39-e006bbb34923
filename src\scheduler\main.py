import boto3
import logging
import os
import fnmatch
from datetime import datetime

# Configure logging
logger = logging.getLogger()
logger.setLevel(logging.INFO)

def should_process_service(service_name, target_services=None, service_patterns=None):
    """
    Determine if a service should be processed based on target services and patterns.

    Parameters:
    - service_name (str): Name of the service to check
    - target_services (list, optional): List of specific service names to include
    - service_patterns (list, optional): List of service name patterns (supports wildcards)

    Returns:
    - bool: True if the service should be processed, False otherwise
    """
    # If no filters are specified, process all services
    if not target_services and not service_patterns:
        return True

    # Check if service is in the target services list
    if target_services and service_name in target_services:
        return True

    # Check if service matches any of the patterns
    if service_patterns:
        for pattern in service_patterns:
            if fnmatch.fnmatch(service_name, pattern):
                return True

    return False

def get_desired_count_for_service(service_name, default_count, service_specific_counts):
    """
    Get the desired count for a specific service.

    Parameters:
    - service_name (str): Name of the service
    - default_count (int): Default desired count
    - service_specific_counts (dict): Service-specific desired counts

    Returns:
    - int: The desired count for the service
    """
    return service_specific_counts.get(service_name, default_count)

def lambda_handler(event, context):
    """
    Lambda function to start or stop services in an ECS cluster.

    The function determines whether to start or stop services based on the 'action' parameter
    in the event. If not specified, it will determine the action based on the current time.

    Parameters:
    - event (dict): Lambda event data, can contain:
        - action (str, optional): 'start' or 'stop'. If not provided, will be determined by time.
        - cluster (str, optional): ECS cluster ARN. If not provided, uses environment variable or default.
        - services (list, optional): List of service names to operate on. If not provided, operates on all services.
        - service_patterns (list, optional): List of service name patterns (supports wildcards). If not provided, operates on all services.
        - desired_count (int, optional): Default desired count for start action. Default is 1.
        - service_desired_counts (dict, optional): Service-specific desired counts. Format: {"service_name": count}
    - context (LambdaContext): Lambda context object

    Returns:
    - dict: Result of the operation
    """
    # Get the ECS cluster ARN from event, environment variable, or use default
    cluster_arn = event.get('cluster',
                           os.environ.get('ECS_CLUSTER_ARN',
                                         'arn:aws-cn:ecs:cn-north-1:051953029949:cluster/msdpd-uatv-ecs-fargate'))

    # Get target services and patterns from event
    target_services = event.get('services', [])
    service_patterns = event.get('service_patterns', [])

    # Get desired count configuration
    default_desired_count = event.get('desired_count', 1)
    service_desired_counts = event.get('service_desired_counts', {})

    # Determine action (start or stop) from event or based on time
    action = event.get('action')

    # If action not specified, determine based on time (example: stop after hours, start during work hours)
    if not action:
        current_hour = datetime.now().hour
        # Assuming work hours are 8 AM to 6 PM (8-18)
        if 8 <= current_hour < 18:
            action = 'start'
        else:
            action = 'stop'

    # Log the operation details
    if target_services:
        logger.info(f"Executing {action} action on cluster {cluster_arn} for specific services: {target_services}")
    elif service_patterns:
        logger.info(f"Executing {action} action on cluster {cluster_arn} for services matching patterns: {service_patterns}")
    else:
        logger.info(f"Executing {action} action on cluster {cluster_arn} for all services")

    # Log desired count configuration for start actions
    if action == 'start':
        if service_desired_counts:
            logger.info(f"Service-specific desired counts: {service_desired_counts}")
        logger.info(f"Default desired count: {default_desired_count}")

    # Initialize ECS client
    ecs_client = boto3.client('ecs', region_name='cn-north-1')

    try:
        # List all services in the cluster
        services = []
        next_token = None

        while True:
            if next_token:
                response = ecs_client.list_services(cluster=cluster_arn, nextToken=next_token)
            else:
                response = ecs_client.list_services(cluster=cluster_arn)

            services.extend(response['serviceArns'])

            if 'nextToken' in response:
                next_token = response['nextToken']
            else:
                break

        if not services:
            logger.info(f"No services found in cluster {cluster_arn}")
            return {
                'statusCode': 200,
                'body': f"No services found in cluster {cluster_arn}"
            }

        logger.info(f"Found {len(services)} services in cluster {cluster_arn}")

        # Filter services based on target services and patterns
        if target_services or service_patterns:
            # Get service names for filtering
            all_service_names = []
            batch_size = 10

            for i in range(0, len(services), batch_size):
                batch = services[i:i + batch_size]
                service_details = ecs_client.describe_services(
                    cluster=cluster_arn,
                    services=batch
                )
                all_service_names.extend([s['serviceName'] for s in service_details['services']])

            # Filter services based on criteria
            filtered_service_names = [
                name for name in all_service_names
                if should_process_service(name, target_services, service_patterns)
            ]

            if not filtered_service_names:
                logger.info(f"No services match the specified criteria")
                return {
                    'statusCode': 200,
                    'body': f"No services match the specified criteria"
                }

            logger.info(f"Filtered to {len(filtered_service_names)} services: {filtered_service_names}")

            # Convert service names back to ARNs for processing
            filtered_services = []
            for service_arn in services:
                service_name = service_arn.split('/')[-1]  # Extract service name from ARN
                if service_name in filtered_service_names:
                    filtered_services.append(service_arn)

            services = filtered_services

        # Process services in batches (ECS API limits to 10 services per call)
        batch_size = 10
        results = []

        for i in range(0, len(services), batch_size):
            batch = services[i:i + batch_size]

            # Get current service details
            service_details = ecs_client.describe_services(
                cluster=cluster_arn,
                services=batch
            )

            for service in service_details['services']:
                service_name = service['serviceName']
                current_count = service['desiredCount']

                if action == 'stop' and current_count > 0:
                    # Stop the service by setting desired count to 0
                    ecs_client.update_service(
                        cluster=cluster_arn,
                        service=service_name,
                        desiredCount=0
                    )
                    logger.info(f"Stopped service {service_name} (reduced from {current_count} to 0)")
                    results.append(f"Stopped {service_name}")

                elif action == 'start':
                    # Determine the desired count for this service
                    target_count = get_desired_count_for_service(
                        service_name,
                        default_desired_count,
                        service_desired_counts
                    )

                    if current_count != target_count:
                        # Update the service to the target desired count
                        ecs_client.update_service(
                            cluster=cluster_arn,
                            service=service_name,
                            desiredCount=target_count
                        )
                        logger.info(f"Updated service {service_name} (changed from {current_count} to {target_count})")
                        results.append(f"Updated {service_name} to {target_count} tasks")
                    else:
                        logger.info(f"Service {service_name} already at target count ({current_count})")
                        results.append(f"No change for {service_name} (already at {current_count})")

                else:
                    logger.info(f"Service {service_name} already in desired state (count: {current_count})")
                    results.append(f"No change for {service_name}")

        return {
            'statusCode': 200,
            'body': f"Successfully processed {len(services)} services. Results: {', '.join(results)}"
        }

    except Exception as e:
        logger.error(f"Error processing ECS services: {str(e)}")
        return {
            'statusCode': 500,
            'body': f"Error: {str(e)}"
        }